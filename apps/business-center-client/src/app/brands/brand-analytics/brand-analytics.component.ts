import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { NavTabsContainerComponent } from '@vendasta/business-nav';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { LocationsService } from '../../locations';

@Component({
  selector: 'bc-brand-analytics',
  template: `
    <glxy-page>
      <glxy-page-toolbar>
        <glxy-page-title>{{ 'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.ANALYTICS' | translate }}</glxy-page-title>
      </glxy-page-toolbar>
      <bc-nav-tabs-container></bc-nav-tabs-container>
    </glxy-page>
  `,
  imports: [GalaxyPageModule, NavTabsContainerComponent, TranslateModule],
  providers: [
    {
      provide: 'NAV_TABS_CONTAINER_ID',
      useValue: 'analytics',
    },
  ],
})
export default class BrandAnalyticsComponent implements OnInit {
  constructor(
    private router: Router,
    private locationsService: LocationsService,
  ) {}

  ngOnInit(): void {
    // Redirect to Google tab by default if we're on the base analytics route
    if (this.router.url.endsWith('/analytics')) {
      this.router.navigate(['./google'], { relativeTo: this.router.routerState.root });
    }
  }
}
