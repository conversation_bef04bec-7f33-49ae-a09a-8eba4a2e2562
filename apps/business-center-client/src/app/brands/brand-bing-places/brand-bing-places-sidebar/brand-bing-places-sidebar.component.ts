import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BingPlacesService } from '../../../metrics/bing-places.service';
import { ConnectedCardsService } from '../../../performance-dashboard/connected-cards/connected-cards.service';

@Component({
  selector: 'bc-brand-bing-places-sidebar',
  template: `
    <div class="sidebar-content">
      <mat-card class="analytics-card">
        <mat-card-header>
          <mat-card-title>{{ 'PERFORMANCE.MULTI_LOCATION.BING.ANALYTICS' | translate }}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="analytics-section">
            <h4>{{ 'PERFORMANCE.MULTI_LOCATION.BING.VIEWS' | translate }}</h4>
            <div class="metric-item">
              <span class="metric-label">{{ 'PERFORMANCE.MULTI_LOCATION.BING.TOTAL_VIEWS' | translate }}</span>
              <span class="metric-value">{{ (totalViews$ | async) || 0 | number }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">{{ 'PERFORMANCE.MULTI_LOCATION.BING.MAP_VIEW' | translate }}</span>
              <span class="metric-value">{{ (mapViews$ | async) || 0 | number }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">{{ 'PERFORMANCE.MULTI_LOCATION.BING.WEB_VIEW' | translate }}</span>
              <span class="metric-value">{{ (webViews$ | async) || 0 | number }}</span>
            </div>
          </div>
          
          <div class="analytics-section">
            <h4>{{ 'PERFORMANCE.MULTI_LOCATION.BING.ACTIONS' | translate }}</h4>
            <div class="metric-item">
              <span class="metric-label">{{ 'PERFORMANCE.MULTI_LOCATION.BING.TOTAL_ACTIONS' | translate }}</span>
              <span class="metric-value">{{ (totalActions$ | async) || 0 | number }}</span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .sidebar-content {
      padding: 16px;
    }
    
    .analytics-card {
      margin-bottom: 16px;
    }
    
    .analytics-section {
      margin-bottom: 24px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }
    }
    
    .metric-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
    }
    
    .metric-label {
      font-size: 13px;
      color: #666;
    }
    
    .metric-value {
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
  `],
  imports: [
    CommonModule,
    MatCardModule,
    TranslateModule,
  ],
})
export class BrandBingPlacesSidebarComponent implements OnInit {
  totalViews$: Observable<number>;
  mapViews$: Observable<number>;
  webViews$: Observable<number>;
  totalActions$: Observable<number>;

  constructor(
    private bingPlacesService: BingPlacesService,
    private connectedCardsService: ConnectedCardsService,
  ) {}

  ngOnInit(): void {
    this.totalViews$ = this.bingPlacesService.currentOverallInsights$.pipe(
      map(insights => insights?.totalViews() || 0)
    );

    this.mapViews$ = this.bingPlacesService.currentOverallInsights$.pipe(
      map(insights => insights?.mapViews() || 0)
    );

    this.webViews$ = this.bingPlacesService.currentOverallInsights$.pipe(
      map(insights => insights?.webViews() || 0)
    );

    this.totalActions$ = this.bingPlacesService.currentOverallInsights$.pipe(
      map(insights => insights?.totalActions() || 0)
    );
  }
}
