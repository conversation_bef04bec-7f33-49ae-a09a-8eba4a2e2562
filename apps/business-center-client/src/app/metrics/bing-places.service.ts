import { Injectable } from '@angular/core';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { Observable } from 'rxjs';
import { filter, map, publishReplay, refCount } from 'rxjs/operators';
import { Row, rowify } from './helpers';
import { QueryService, StatByTime } from './query.service';
import { StatByDimension } from './reviews.service';
dayjs.extend(utc);

export class BingInsightsData {
  constructor(
    public views_maps_mobile: number,
    public views_maps_desktop: number,
    public views_web_mobile: number,
    public views_web_desktop: number,
    public actions_website: number,
    public actions_directions: number,
    public actions_phone: number,
    public actions_photo: number,
    public actions_located_at: number,
    public actions_review: number,
    public actions_menu: number,
    public actions_order_online: number,
    public actions_suggest_edit: number,
  ) {
    this.total_views = this.views_maps_mobile + this.views_maps_desktop + this.views_web_mobile + this.views_web_desktop;
    this.map_views = this.views_maps_mobile + this.views_maps_desktop;
    this.web_views = this.views_web_mobile + this.views_web_desktop;
    this.total_actions =
      this.actions_website +
      this.actions_directions +
      this.actions_phone +
      this.actions_photo +
      this.actions_located_at +
      this.actions_review +
      this.actions_menu +
      this.actions_order_online +
      this.actions_suggest_edit;
  }
  public total_views: number;
  public map_views: number;
  public web_views: number;
  public total_actions: number;

  static empty(): BingInsightsData {
    const a = new Array(13).fill(0);
    return this.fromArray(a);
  }

  static fromArray(a: string[]): BingInsightsData {
    if (!a) {
      return null;
    }
    // convert the array to numbers
    const b: number[] = [];
    a.forEach((x) => {
      b.push(Number(x));
    });
    return new BingInsightsData(
      b[0], b[1], b[2], b[3], b[4], b[5], b[6], b[7], b[8], b[9], b[10], b[11], b[12]
    );
  }

  static fromRow(row: Row): BingInsightsData {
    if (!row) {
      return null;
    }
    // Interpret measures in order
    const m = row.measures[0].map((r) => Number(r));
    return new BingInsightsData(
      m[0], m[1], m[2], m[3], m[4], m[5], m[6], m[7], m[8], m[9], m[10], m[11], m[12]
    );
  }

  public totalViews(): number {
    return this.total_views;
  }

  public totalActions(): number {
    return this.total_actions;
  }

  public mapViews(): number {
    return this.map_views;
  }

  public webViews(): number {
    return this.web_views;
  }
}

@Injectable({ providedIn: 'root' })
export class BingPlacesService {
  readonly queryDateString$: Observable<string>;
  readonly connectedLocations$: Observable<Map<string, number>>;

  readonly currentOverallInsights$: Observable<BingInsightsData>;
  readonly previousOverallInsights$: Observable<BingInsightsData>;

  readonly previousInsightsByLocation$: Observable<StatByDimension<BingInsightsData>[]>;
  readonly currentInsightsByLocation$: Observable<StatByDimension<BingInsightsData>[]>;

  readonly currentInsightsByTime$: Observable<StatByTime<BingInsightsData>[]>;

  constructor(private readonly queryService: QueryService) {
    this.queryDateString$ = this.queryService.brandsContext$.pipe(
      map((brandContext) => {
        const [startDate, endDate] = brandContext.BingDateRange(true);
        const start = dayjs.utc(startDate).format('MMM Do, YYYY');
        const end = dayjs.utc(endDate).format('MMM Do, YYYY');
        return start + ' - ' + end;
      }),
    );

    this.connectedLocations$ = this.getConnectedLocations();

    // Combine view and action data
    this.currentOverallInsights$ = combineLatest([
      this.queryService.bingViewInsights$s.get('account_group_id').currentPeriodRows$,
      this.queryService.bingActionsInsights$s.get('account_group_id').currentPeriodRows$,
    ]).pipe(
      map(([viewRows, actionRows]) => {
        if (viewRows == null || actionRows == null) {
          return null;
        }
        const viewBrandRow = viewRows.find((r) => r.values[0] === null && r.values[1] === null);
        const actionBrandRow = actionRows.find((r) => r.values[0] === null && r.values[1] === null);

        if (!viewBrandRow || !actionBrandRow) {
          return null;
        }

        // Combine view and action data
        const combinedMeasures = [
          ...viewBrandRow.measures[0], // view measures
          ...actionBrandRow.measures[0], // action measures
        ];

        return BingInsightsData.fromArray(combinedMeasures);
      }),
    );

    this.previousOverallInsights$ = combineLatest([
      this.queryService.bingViewInsights$s.get('account_group_id').previousPeriodRows$,
      this.queryService.bingActionsInsights$s.get('account_group_id').previousPeriodRows$,
    ]).pipe(
      map(([viewRows, actionRows]) => {
        if (viewRows == null || actionRows == null) {
          return null;
        }
        const viewBrandRow = viewRows.find((r) => r.values[0] === null && r.values[1] === null);
        const actionBrandRow = actionRows.find((r) => r.values[0] === null && r.values[1] === null);

        if (!viewBrandRow || !actionBrandRow) {
          return null;
        }

        // Combine view and action data
        const combinedMeasures = [
          ...viewBrandRow.measures[0], // view measures
          ...actionBrandRow.measures[0], // action measures
        ];

        return BingInsightsData.fromArray(combinedMeasures);
      }),
    );

    this.currentInsightsByLocation$ = combineLatest([
      this.queryService.bingViewInsights$s.get('account_group_id').currentPeriodRows$,
      this.queryService.bingActionsInsights$s.get('account_group_id').currentPeriodRows$,
    ]).pipe(
      map(([viewRows, actionRows]) => {
        if (viewRows == null || actionRows == null) {
          return null;
        }
        const viewLocationRows = viewRows.filter((r) => !!r.values[0] && r.values[1] === null);
        const actionLocationRows = actionRows.filter((r) => !!r.values[0] && r.values[1] === null);

        // Create a map to combine data by location
        const locationDataMap = new Map<string, BingInsightsData>();

        viewLocationRows.forEach((viewRow) => {
          const locationId = viewRow.values[0];
          const viewMeasures = viewRow.measures[0];
          const actionRow = actionLocationRows.find((r) => r.values[0] === locationId);
          const actionMeasures = actionRow ? actionRow.measures[0] : new Array(9).fill('0');

          const combinedMeasures = [...viewMeasures, ...actionMeasures];
          locationDataMap.set(locationId, BingInsightsData.fromArray(combinedMeasures));
        });

        return Array.from(locationDataMap.entries()).map(([locationId, data]) => {
          return new StatByDimension<BingInsightsData>(locationId, data);
        });
      }),
    );

    this.previousInsightsByLocation$ = combineLatest([
      this.queryService.bingViewInsights$s.get('account_group_id').previousPeriodRows$,
      this.queryService.bingActionsInsights$s.get('account_group_id').previousPeriodRows$,
    ]).pipe(
      map(([viewRows, actionRows]) => {
        if (viewRows == null || actionRows == null) {
          return null;
        }
        const viewLocationRows = viewRows.filter((r) => !!r.values[0] && r.values[1] === null);
        const actionLocationRows = actionRows.filter((r) => !!r.values[0] && r.values[1] === null);

        // Create a map to combine data by location
        const locationDataMap = new Map<string, BingInsightsData>();

        viewLocationRows.forEach((viewRow) => {
          const locationId = viewRow.values[0];
          const viewMeasures = viewRow.measures[0];
          const actionRow = actionLocationRows.find((r) => r.values[0] === locationId);
          const actionMeasures = actionRow ? actionRow.measures[0] : new Array(9).fill('0');

          const combinedMeasures = [...viewMeasures, ...actionMeasures];
          locationDataMap.set(locationId, BingInsightsData.fromArray(combinedMeasures));
        });

        return Array.from(locationDataMap.entries()).map(([locationId, data]) => {
          return new StatByDimension<BingInsightsData>(locationId, data);
        });
      }),
    );

    this.currentInsightsByTime$ = combineLatest([
      this.queryService.bingViewInsights$s.get('account_group_id').currentPeriodRows$,
      this.queryService.bingActionsInsights$s.get('account_group_id').currentPeriodRows$,
    ]).pipe(
      map(([viewRows, actionRows]) => {
        if (viewRows == null || actionRows == null) {
          return null;
        }
        const viewTimeRows = viewRows.filter((r) => r.values[0] === null && !!r.values[1]);
        const actionTimeRows = actionRows.filter((r) => r.values[0] === null && !!r.values[1]);

        // Create a map to combine data by time
        const timeDataMap = new Map<string, BingInsightsData>();

        viewTimeRows.forEach((viewRow) => {
          const timeKey = viewRow.values[1];
          const viewMeasures = viewRow.measures[0];
          const actionRow = actionTimeRows.find((r) => r.values[1] === timeKey);
          const actionMeasures = actionRow ? actionRow.measures[0] : new Array(9).fill('0');

          const combinedMeasures = [...viewMeasures, ...actionMeasures];
          timeDataMap.set(timeKey, BingInsightsData.fromArray(combinedMeasures));
        });

        return Array.from(timeDataMap.entries())
          .map(([timeKey, data]) => {
            return new StatByTime<BingInsightsData>(dayjs(timeKey).toDate(), data);
          })
          .sort((a, b) => a.time.getTime() - b.time.getTime());
      }),
    );
  }

  public getConnectedLocations(): Observable<Map<string, number>> {
    return this.queryService.bingConnections$.pipe(
      map((rows) => {
        if (rows == null) {
          return null;
        }
        const m = new Map();
        rowify(rows.metricResults[0]).forEach((r) => {
          m.set(r.values[0], Number(r.measures[0]));
        });
        return m;
      }),
      filter((r) => r !== null),
      publishReplay(1),
      refCount(),
    );
  }
}
